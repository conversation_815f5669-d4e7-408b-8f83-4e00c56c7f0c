<route lang="json5">
{
  style: {
    navigationBarTitleText: '非教学场地使用申请表单',
  },
}
</route>
<script setup lang="ts">
import { ref, computed } from 'vue'
import DetailedApplyForm from '../components/DetailedApplyForm.vue'
import { getFreeRoomsApi } from '@/service/site-use-apply'
import type { FreeRoomInfo, RoomDisplayItem, CourseInfo } from '@/types/site-use-apply'

// 表单引用
const formRef = ref()

// 表单数据
const formData = ref<any>({})

// 空闲教室数据
const freeRooms = ref<FreeRoomInfo[]>([])
const loading = ref(false)

// 处理表单数据变化
const handleFormChange = (data: any) => {
  formData.value = data
  // 当日期或节次变化时，重新获取空闲教室
  if (data.usageDate && data.jc) {
    fetchFreeRooms()
  }
}

// 获取空闲教室列表
const fetchFreeRooms = async () => {
  if (!formData.value.usageDate || !formData.value.jc) return

  loading.value = true
  try {
    const params = {
      sysj: formData.value.usageDate,
      syjc: formData.value.jc,
      sycd: '',
    }
    const result = await getFreeRoomsApi(params)
    freeRooms.value = result
  } catch (error) {
    console.error('获取空闲教室失败:', error)
    freeRooms.value = []
  } finally {
    loading.value = false
  }
}

// 计算显示的教室列表
const displayRooms = computed<RoomDisplayItem[]>(() => {
  if (!formData.value.jcArr || !formData.value.jcShowArr) return []

  return freeRooms.value.map((room) => {
    const courses: CourseInfo[] = []
    const periods: string[] = []

    // 根据选中的节次获取课程信息
    formData.value.jcArr.forEach((jc: string, index: number) => {
      const jcKey = `jc${jc}` as keyof FreeRoomInfo
      const jcCourses = room[jcKey]

      if (jcCourses && Array.isArray(jcCourses) && jcCourses.length > 0) {
        courses.push(...jcCourses)
      }

      // 添加节次显示
      if (formData.value.jcShowArr[index]) {
        periods.push(`第${formData.value.jcShowArr[index]}节`)
      }
    })

    return {
      sycddm: room.sycddm,
      sycdmc: room.sycdmc,
      ssxqmc: room.ssxqmc,
      ssjzlmc: room.ssjzlmc,
      bjrl: room.bjrl,
      available: room.showRadio && courses.length === 0,
      courses,
      periodInfo: {
        date: formData.value.usageDate,
        periods,
      },
    }
  })
})

// 暴露表单数据给父组件
defineExpose({
  formData,
  validate: () => formRef.value?.validate(),
  reset: () => formRef.value?.reset(),
})
</script>

<template>
  <view class="apply-form-page">
    <!-- 申请表单 -->
    <DetailedApplyForm ref="formRef" v-model="formData" @change="handleFormChange" />

    <!-- 空闲教室列表 -->
    <view v-if="formData.usageDate && formData.jc" class="room-list-section">
      <view class="section-header">
        <text class="section-title">空闲教室列表</text>
        <text v-if="formData.usageDate && displayRooms.length > 0" class="section-subtitle">
          {{ formData.usageDate }} {{ displayRooms[0]?.periodInfo.periods.join('、') }}
        </text>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <wd-loading />
        <text class="loading-text">正在加载教室信息...</text>
      </view>

      <!-- 教室列表 -->
      <view v-else-if="displayRooms.length > 0" class="room-list">
        <view
          v-for="room in displayRooms"
          :key="room.sycddm"
          class="room-item"
          :class="{ 'room-occupied': !room.available }"
        >
          <view class="room-header">
            <view class="room-info">
              <text class="room-name">{{ room.sycdmc }}</text>
              <text class="room-location">{{ room.ssxqmc }} {{ room.ssjzlmc }}</text>
            </view>
            <view class="room-status">
              <text v-if="room.available" class="status-available">空闲</text>
              <text v-else class="status-occupied">占用</text>
            </view>
          </view>

          <!-- 课程信息 -->
          <view v-if="room.courses.length > 0" class="course-info">
            <view v-for="(course, index) in room.courses" :key="index" class="course-item">
              <text class="course-name">{{ course.kcmc }}</text>
              <text class="course-teacher">{{ course.syrxm }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 无数据状态 -->
      <view v-else class="empty-state">
        <text class="empty-text">暂无教室信息</text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.apply-form-page {
  padding-bottom: 32rpx;
}

.room-list-section {
  padding: 0 32rpx;
  margin-top: 32rpx;
}

.section-header {
  margin-bottom: 24rpx;

  .section-title {
    display: block;
    margin-bottom: 8rpx;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }

  .section-subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;

  .loading-text {
    margin-top: 16rpx;
    font-size: 28rpx;
    color: #999;
  }
}

.room-list {
  .room-item {
    padding: 24rpx;
    margin-bottom: 16rpx;
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    &.room-occupied {
      background: #f8f8f8;
      border-left: 8rpx solid #ff6b6b;
    }

    .room-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 16rpx;

      .room-info {
        flex: 1;

        .room-name {
          display: block;
          margin-bottom: 8rpx;
          font-size: 32rpx;
          font-weight: 600;
          color: #333;
        }

        .room-location {
          display: block;
          font-size: 26rpx;
          color: #666;
        }
      }

      .room-status {
        .status-available {
          padding: 8rpx 16rpx;
          font-size: 24rpx;
          color: #fff;
          background: #52c41a;
          border-radius: 8rpx;
        }

        .status-occupied {
          padding: 8rpx 16rpx;
          font-size: 24rpx;
          color: #fff;
          background: #ff6b6b;
          border-radius: 8rpx;
        }
      }
    }

    .course-info {
      padding-top: 16rpx;
      border-top: 1px solid #f0f0f0;

      .course-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8rpx 0;

        .course-name {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }

        .course-teacher {
          font-size: 26rpx;
          color: #666;
        }
      }
    }
  }
}

.empty-state {
  padding: 80rpx 0;
  text-align: center;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
