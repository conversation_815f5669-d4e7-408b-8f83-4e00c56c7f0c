<route lang="json5">
{
  style: {
    navigationBarTitleText: '非教学场地使用申请表单',
  },
}
</route>
<script setup lang="ts">
import { ref } from 'vue'
import DetailedApplyForm from '../components/DetailedApplyForm.vue'

// 表单引用
const formRef = ref()

// 表单数据
const formData = ref({})

// 处理表单数据变化
const handleFormChange = (data: any) => {
  formData.value = data
}

// 暴露表单数据给父组件
defineExpose({
  formData,
  validate: () => formRef.value?.validate(),
  reset: () => formRef.value?.reset(),
})
</script>

<template>
  <DetailedApplyForm ref="formRef" v-model="formData" @change="handleFormChange" />
</template>

<style lang="scss" scoped>
// 申请表单页面样式（现在使用组件，无需额外样式）
</style>
