/**
 * 场地使用申请相关API
 */

import { request } from '@/utils/request'
import type { FreeRoomsRequest, FreeRoomsResponse } from '@/types/site-use-apply'

/**
 * 获取空闲教室列表
 * @param params 查询参数
 * @returns 空闲教室列表
 */
export function getFreeRoomsApi(params: FreeRoomsRequest): Promise<FreeRoomsResponse['data']> {
  return request<FreeRoomsResponse>({
    url: '/siteUseApply/getFreeRooms',
    method: 'POST',
    data: params,
  }).then((res) => res.data)
}
